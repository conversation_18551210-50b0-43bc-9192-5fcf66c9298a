import clsx from 'clsx'

export function SidebarHeader({ className, children, ...props }: React.ComponentProps<'div'>) {
    return (
        <div
            className={clsx(
                'flex flex-row group-data-[state=collapsed]:flex-col group-data-[state=collapsed]:items-center items-center justify-between gap-5 p-4',
                className,
            )}
            {...props}
        >
            {children}
        </div>
    )
}
