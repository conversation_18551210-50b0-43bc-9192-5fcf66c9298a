import clsx from 'clsx'

export function SidebarContent({ className, ...props }: React.ComponentProps<'div'>) {
    return (
        <nav
            className={clsx(
                'flex min-h-0 flex-1 flex-col gap-2 overflow-y-auto overflow-x-hidden',
                'scrollbar-thin scrollbar-track-transparent scrollbar-thumb-sidebar-accent/20 hover:scrollbar-thumb-sidebar-accent/40',
                className,
            )}
            {...props}
        />
    )
}
