import { Link } from '@tanstack/react-router'
import { useSidebarStore } from '@/services/sidebar'
import logo from './../../assets/Logo.svg'
import logoText from './../../assets/logo-text.svg'

export function SidebarLogo() {
    const { isOpen } = useSidebarStore()

    return (
        <>
            <Link
                to="/"
                className="flex flex-row gap-3 shrink-0"
            >
                <img
                    src={logo}
                    alt="Логотип Simbios"
                />
                {isOpen && <img src={logoText} />}
            </Link>
        </>
    )
}
