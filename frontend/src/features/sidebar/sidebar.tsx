import clsx from 'clsx'
import { MessageSquareMore, PanelRightOpen } from 'lucide-react'

import { useSidebarStore } from '@/services/sidebar'

import { SidebarContent } from './ui/kit/sidebar-content'
import { SidebarFooter } from './ui/kit/sidebar-footer'
import { SidebarHeader } from './ui/kit/sidebar-header'

import { SidebarMenuButton } from './ui/kit/sidebar-menu'
import { SidebarLogo } from './ui/kit/sidebar-logo'
import { MainNav } from './main-nav'

export const SIDEBAR_ICON_SIZE = '1.25rem'

export function Sidebar() {
    const { isOpen, toggleOpen } = useSidebarStore()

    return (
        <aside
            className={clsx(
                `group shrink-0 h-screen sticky top-0 text-sidebar-primary-foreground ease-linear overflow-y-auto overflow-x-hidden bg-sidebar transition-[width] duration-100`,
                isOpen ? 'w-[var(--sidebar-width-expanded)]' : 'w-[var(--sidebar-width-collapsed)]',
            )}
            data-state={isOpen ? 'open' : 'collapsed'}
        >
            <div className="flex flex-col h-full w-full overflow-hidden">
                <SidebarHeader>
                    <SidebarLogo />
                    <button
                        type="button"
                        onClick={toggleOpen}
                    >
                        <PanelRightOpen size={SIDEBAR_ICON_SIZE} />
                    </button>
                </SidebarHeader>
                <SidebarContent>
                    <MainNav />
                </SidebarContent>
                <SidebarFooter className="pt-0">
                    <SidebarMenuButton
                        variant={'footer'}
                        size={'lg'}
                    >
                        <MessageSquareMore size={SIDEBAR_ICON_SIZE} />
                        <span>Мессенджер</span>
                    </SidebarMenuButton>
                </SidebarFooter>
            </div>
        </aside>
    )
}
