import { Box, Folder, GitFork, Gpu, Home } from 'lucide-react'
import { SidebarGroup, SidebarGroupLabel } from './ui/kit/sidebar-group'
import { SidebarMenu, SidebarMenuLink } from './ui/kit/sidebar-menu'
import { SIDEBAR_ICON_SIZE } from './sidebar'
import { Separator } from './ui/kit/sidebar-separator'

interface MenuItem {
    href: string
    label: string
    icon: React.ComponentType<{ size: string }>
}

interface MenuGroup {
    title: string
    items: MenuItem[]
}

const menuData: MenuGroup[] = [
    {
        title: 'Основное',
        items: [
            { href: '/', label: 'Рабочий стол', icon: Home },
            // TODO PLACEHOLDER поменять на реальный эндпоинт
            { href: '/test', label: '!Тест!', icon: Folder },
        ],
    },
    {
        title: 'Планирование',
        items: [
            { href: '#', label: 'Календарный план', icon: GitFork },
            { href: '/objects', label: 'Сетевой график', icon: Box },
            { href: '/processes', label: 'Бюджет', icon: Gpu },
        ],
    },
    {
        title: 'Ресурсы',
        items: [
            { href: '#', label: 'Команда', icon: GitFork },
            { href: '/company', label: 'Структура компании', icon: Box },
            { href: '/processes', label: 'Матрица ответственности', icon: Gpu },
        ],
    },
    {
        title: 'Аналитика',
        items: [
            { href: '#', label: 'Отчёты и метрики', icon: GitFork },
            { href: '/objects', label: 'Паспорт проекта', icon: Box },
        ],
    },
    {
        title: 'Настройки',
        items: [
            { href: '#', label: 'Настройки', icon: GitFork },
        ],
    },
]

export function MainNav() {
    return (
        <div className="space-y-2">
            {menuData.map((group, groupIndex) => (
                <div key={group.title}>
                    <SidebarGroup>
                        <SidebarGroupLabel>{group.title}</SidebarGroupLabel>
                        <SidebarMenu>
                            {group.items.map((item) => {
                                const Icon = item.icon
                                return (
                                    <SidebarMenuLink
                                        key={`${item.href}`}
                                        href={item.href}
                                    >
                                        <Icon size={SIDEBAR_ICON_SIZE} />
                                        <span>{item.label}</span>
                                    </SidebarMenuLink>
                                )
                            })}
                        </SidebarMenu>
                    </SidebarGroup>
                    {groupIndex < menuData.length - 1 && <Separator />}
                </div>
            ))}
        </div>
    )
}
