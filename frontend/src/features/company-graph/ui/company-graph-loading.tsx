import { memo } from 'react'

// TODO переделать под дизайн, добавить спиннер из ui kit
export const CompanyGraphLoading = memo(function CompanyGraphLoading() {
    return (
        <div
            className="top-0 bottom-0 left-0 right-0 bg-card z-50 rounded-xl absolute flex items-center justify-center "
            data-testid="company-graph-loading"
        >
            <div className="flex flex-col items-center space-y-4">
                <div className="relative">
                    <div className="w-16 h-16 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin"></div>
                    <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-blue-400 rounded-full animate-spin animation-delay-150"></div>
                </div>

                <div className="text-center">
                    <h3 className="text-lg font-semibold text-gray-700 mb-2">
                        Загрузка структуры компании
                    </h3>
                    <p className="text-sm text-gray-500">Подготавливаем граф...</p>
                </div>
            </div>
        </div>
    )
})

CompanyGraphLoading.displayName = 'CompanyGraphLoading'
