import { Handle, Position } from '@xyflow/react'
import { memo } from 'react'
import type { Employee } from '../models/types'

interface EmployeeNodeProps {
    data: Employee
}

export const EmployeeNode = memo(function EmployeeNode({ data }: EmployeeNodeProps) {
    return (
        <>
            <Handle
                type="target"
                position={Position.Top}
                className="opacity-0 h-0! -top-7!"
                aria-label="Connection point"
            />
            <div className="w-[240px] flex flex-col items-center border rounded-xl border-t-4 border-t-sidebar-accent bg-white overflow-hidden">
                <div className="flex-shrink-0 absolute -top-7.5 left-1/2 transform -translate-x-1/2">
                    <img
                        src={data.avatar}
                        alt={`${data.name} avatar`}
                        className="w-[60px] h-[60px] rounded-full border-2 border-white"
                    />
                </div>
                <div className="flex flex-col gap-0.5 px-1 pt-8 pb-1.5 items-center">
                    <div className="flex flex-col justify-center items-center">
                        <p className="text-lg">{data.name}</p>
                        <p className="text-sm text-muted-foreground">{data.job_title}</p>
                    </div>
                    <p className="text-sm text-muted-foreground">{data.department_name}</p>
                </div>
            </div>
            <Handle
                type="source"
                position={Position.Bottom}
                className="opacity-0 h-0! bottom-1!"
                aria-label="Connection point"
            />
        </>
    )
})
