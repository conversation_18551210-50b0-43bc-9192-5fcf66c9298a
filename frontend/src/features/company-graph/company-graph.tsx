import { memo } from 'react'
import { ReactFlowProvider } from '@xyflow/react'

import { useOrganizationalData } from './models/use-organizational-data'
import { CompanyGraphFlow } from './company-graph-flow'
import { CompanyGraphLoading } from './ui/company-graph-loading'

interface CompanyGraphProps {
    className?: string
}

export const CompanyGraph = memo(function CompanyGraph({ className }: CompanyGraphProps = {}) {
    const { data, isLoading } = useOrganizationalData()

    if (isLoading || !data) {
        return <CompanyGraphLoading />
    }

    return (
        <ReactFlowProvider>
            <CompanyGraphFlow
                initialNodes={data.nodes}
                initialEdges={data.edges}
                className={className}
            />
        </ReactFlowProvider>
    )
})
