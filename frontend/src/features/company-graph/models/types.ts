import type { Node, Edge } from '@xyflow/react'

export interface Employee extends Record<string, unknown> {
    id: string
    name: string
    job_title: string
    department_name?: string
    avatar?: string
}

export type EmployeeNode = Node<Employee>
export type EmployeeEdge = Edge

export interface CompanyData {
    nodes: EmployeeNode[]
    edges: EmployeeEdge[]
}

export interface GraphLayoutConfig {
    direction: 'TB' | 'LR' | 'BT' | 'RL'
    nodeWidth: number
    nodeHeight: number
    nodeSpacing: number
    rankSpacing: number
}
