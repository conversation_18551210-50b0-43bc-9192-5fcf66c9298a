import { useMemo } from 'react'
import dagre from '@dagrejs/dagre'
import type { EmployeeEdge, EmployeeNode } from '../models/types'

const LAYOUT_CONFIG = {
    nodeWidth: 200,
    nodeHeight: 120,
    nodeSpacing: 25,
    rankSpacing: 50,
} as const

interface UseGraphLayoutOptions {
    nodeWidth?: number
    nodeHeight?: number
    nodeSpacing?: number
    rankSpacing?: number
}

/**
 * Хук для лейаута графа с помощью Dagre
 */
export function useGraphLayout(
    nodes: EmployeeNode[],
    edges: EmployeeEdge[],
    options: UseGraphLayoutOptions = {},
) {
    const {
        nodeWidth = LAYOUT_CONFIG.nodeWidth,
        nodeHeight = LAYOUT_CONFIG.nodeHeight,
        nodeSpacing = LAYOUT_CONFIG.nodeSpacing,
        rankSpacing = LAYOUT_CONFIG.rankSpacing,
    } = options

    const layoutedElements = useMemo(() => {
        if (nodes.length === 0) {
            return { nodes: [], edges }
        }

        const dagreGraph = new dagre.graphlib.Graph()
        dagreGraph.setDefaultEdgeLabel(() => ({}))

        dagreGraph.setGraph({
            rankdir: 'TB',
            nodesep: nodeSpacing,
            ranksep: rankSpacing,
        })

        for (const node of nodes) {
            dagreGraph.setNode(node.id, { width: nodeWidth, height: nodeHeight })
        }

        for (const edge of edges) {
            dagreGraph.setEdge(edge.source, edge.target)
        }

        dagre.layout(dagreGraph)

        const layoutedNodes = nodes.map((node) => {
            const nodeWithPosition = dagreGraph.node(node.id)

            return {
                ...node,
                position: {
                    x: nodeWithPosition.x - nodeWidth / 2,
                    y: nodeWithPosition.y - nodeHeight / 2,
                },
            }
        })

        return { nodes: layoutedNodes, edges }
    }, [
        nodes,
        edges,
        nodeWidth,
        nodeHeight,
        nodeSpacing,
        rankSpacing,
    ])

    return {
        nodes: layoutedElements.nodes,
        edges: layoutedElements.edges,
        layoutConfig: {
            nodeWidth,
            nodeHeight,
            nodeSpacing,
            rankSpacing,
        },
    }
}
