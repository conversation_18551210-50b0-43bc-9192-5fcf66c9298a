import type { <PERSON>a, StoryObj } from '@storybook/react-vite'

import { Button } from './button'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from './dropdown'
import { Plus } from 'lucide-react'

const meta = {
    component: DropdownMenu,
} satisfies Meta<typeof DropdownMenu>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    name: 'Default - профиль задачи',
    args: {
        children: [
            <DropdownMenuTrigger
                key="trigger"
                asChild
                className="w-[240px]"
            >
                <Button variant="accent">
                    Назначить сотрудника
                    <Plus className="size-4 text-white" />
                </Button>
            </DropdownMenuTrigger>,
            <DropdownMenuContent
                key="content"
                side="bottom"
                className="w-[240px]"
            >
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
                <DropdownMenuItem>Фёдор Авдеев</DropdownMenuItem>
            </DropdownMenuContent>,
        ],
    },
}
