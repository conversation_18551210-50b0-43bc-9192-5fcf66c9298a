import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite'

import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from './select'

const meta = {
    component: Select,
} satisfies Meta<typeof Select>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: [
            <SelectTrigger
                key="trigger"
                className="w-[250px]"
            >
                <SelectValue placeholder="Выберите курс" />
            </SelectTrigger>,
            <SelectContent
                key="content"
                position="popper"
            >
                <SelectGroup>
                    <SelectItem value="design">Веб-дизайн</SelectItem>
                    <SelectItem value="databases">Базы данных</SelectItem>
                    <SelectItem
                        value="coding"
                        disabled
                    >
                        Программирование
                    </SelectItem>
                    <SelectItem value="business">Бизнес</SelectItem>
                    <SelectItem value="sales">Продажи и маркетинг</SelectItem>
                </SelectGroup>
            </SelectContent>,
        ],
    },
}
