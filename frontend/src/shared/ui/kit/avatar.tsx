'use client'

import * as React from 'react'
import * as AvatarPrimitive from '@radix-ui/react-avatar'

import { cn } from '@/shared/lib/css'
import { cva, type VariantProps } from 'class-variance-authority'

const avatarVariants = cva('relative flex size-8 shrink-0 overflow-hidden rounded-full', {
    variants: {
        size: {
            default: 'size-10',
            sm: 'size-7',
        },
    },
    defaultVariants: {
        size: 'default',
    },
})

function Avatar({
    className,
    size,
    ...props
}: React.ComponentProps<typeof AvatarPrimitive.Root> & VariantProps<typeof avatarVariants>) {
    return (
        <AvatarPrimitive.Root
            data-slot="avatar"
            className={cn(avatarVariants({ size }), className)}
            {...props}
        />
    )
}

function AvatarImage({ className, ...props }: React.ComponentProps<typeof AvatarPrimitive.Image>) {
    return (
        <AvatarPrimitive.Image
            data-slot="avatar-image"
            className={cn('aspect-square size-full', className)}
            {...props}
        />
    )
}

function AvatarFallback({
    className,
    ...props
}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {
    return (
        <AvatarPrimitive.Fallback
            data-slot="avatar-fallback"
            className={cn(
                'bg-muted flex size-full items-center justify-center rounded-full',
                className,
            )}
            {...props}
        />
    )
}

function AvatarRow({ children }: { children: React.ReactNode }) {
    return (
        <div className="*:data-[slot=avatar]:ring-background flex -space-x-2 *:data-[slot=avatar]:ring-4">
            {children}
        </div>
    )
}

export { Avatar, AvatarImage, AvatarFallback, AvatarRow }
