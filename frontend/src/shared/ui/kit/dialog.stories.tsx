import type { Meta, StoryObj } from '@storybook/react-vite'

import { <PERSON><PERSON>, DialogContent, DialogFooter, DialogTitle, DialogTrigger } from './dialog'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from './select'
import { Button } from './button'

const meta = {
    component: Dialog,
} satisfies Meta<typeof Dialog>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: () => (
        <Dialog>
            <DialogTrigger>Открыть</DialogTrigger>
            <DialogContent
                size={'default'}
                variant={'default'}
            >
                <DialogTitle>Создать событие</DialogTitle>
                <Select>
                    <div>
                        <p className="mb-2">Выберите тип события</p>
                        <SelectTrigger
                            key="trigger"
                            className="w-full"
                        >
                            <SelectValue placeholder="Выбрать" />
                        </SelectTrigger>
                        <SelectContent
                            key="content"
                            position="popper"
                        >
                            <SelectGroup>
                                <SelectItem value="design">Веб-дизайн</SelectItem>
                                <SelectItem value="databases">Базы данных</SelectItem>
                                <SelectItem
                                    value="coding"
                                    disabled
                                >
                                    Программирование
                                </SelectItem>
                                <SelectItem value="business">Бизнес</SelectItem>
                                <SelectItem value="sales">Продажи и маркетинг</SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </div>
                </Select>
                <Button variant={'accent'}>Создать</Button>
            </DialogContent>
        </Dialog>
    ),
}

export const Transparent: Story = {
    render: () => (
        <Dialog>
            <DialogTrigger>Открыть</DialogTrigger>
            <DialogContent
                size={'default'}
                variant={'transparent'}
            >
                <DialogTitle>Фёдор Авдеев</DialogTitle>
                <DialogFooter>
                    <Button
                        className="self-start"
                        variant={'primary'}
                    >
                        Профиль сотрудника
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    ),
}
