import type { Meta, StoryObj } from '@storybook/react-vite'

import { ToggleGroup, ToggleGroupItem } from './toggle-group'

const meta = {
    component: ToggleGroup,
} satisfies Meta<typeof ToggleGroup>

export default meta

type Story = StoryObj<typeof meta>

export const DefaultSingle: Story = {
    args: {
        defaultValue: 'weeks',
        type: 'single',
        size: 'default',
        variant: 'default',
    },
    render: (args) => (
        <ToggleGroup {...args}>
            <ToggleGroupItem value="weeks">Недели</ToggleGroupItem>
            <ToggleGroupItem value="months">Месяцы</ToggleGroupItem>
        </ToggleGroup>
    ),
}
