import type { <PERSON>a, StoryObj } from '@storybook/react-vite'

import { Badge } from './badge'

const meta = {
    component: Badge,
} satisfies Meta<typeof Badge>

export default meta

type Story = StoryObj<typeof meta>

export const AccentClearDef: Story = {
    name: 'Default - тег в карточке календаря',
    args: {
        children: 'Тег',
        variant: 'accentClear',
        size: 'default',
    },
}

export const DestructiveLg: Story = {
    name: 'Destructive, Large - паспорт проекта, прогресс',
    args: {
        children: 'Поставка (высокий)',
        variant: 'destructive',
        size: 'lg',
    },
}

export const WarningLg: Story = {
    name: 'Warning, Large - паспорт проекта, прогресс',
    args: {
        children: 'Требования (средний)',
        variant: 'warning',
        size: 'lg',
    },
}

export const AccentLg: Story = {
    name: 'Accent, Large - паспорт проекта, прогресс',
    args: {
        children: 'Команда (низкий)',
        variant: 'accent',
        size: 'lg',
    },
}

export const AccentСlearSm: Story = {
    name: 'AccentСlear, Small - паспорт проекта, таблица',
    args: {
        children: 'Завершена',
        variant: 'accentClear',
        size: 'sm',
    },
}

export const RaciqBlue: Story = {
    name: 'RACIQ, R - профиль сотрудника, таблица',
    args: {
        children: 'R',
        variant: 'outlineR',
        size: 'raciq',
    },
}
