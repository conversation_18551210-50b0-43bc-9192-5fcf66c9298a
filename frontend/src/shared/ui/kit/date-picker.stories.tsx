/* eslint-disable react-hooks/rules-of-hooks */
import type { Meta, StoryObj } from '@storybook/react-vite'

import { DatePicker } from './date-picker'
import { useState } from 'react'
import { ru } from 'react-day-picker/locale'

const meta = {
    component: DatePicker,
} satisfies Meta<typeof DatePicker>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    render: () => {
        const [date, setDate] = useState<Date | undefined>()
        const [isOpen, setIsOpen] = useState(false)

        return (
            <DatePicker
                locale={ru}
                date={date}
                setDate={setDate}
                isOpen={isOpen}
                setIsOpen={setIsOpen}
            />
        )
    },
}
