import type { <PERSON>a, StoryObj } from '@storybook/react-vite'

import { Switch } from './switch'

const meta = {
    component: Switch,
    argTypes: {
        disabled: {
            control: 'boolean',
        },
        checked: {
            control: 'boolean',
        },
    },
} satisfies Meta<typeof Switch>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        defaultChecked: true,
        disabled: false,
    },
}

export const Disabled: Story = {
    args: {
        defaultChecked: true,
        disabled: true,
    },
}
