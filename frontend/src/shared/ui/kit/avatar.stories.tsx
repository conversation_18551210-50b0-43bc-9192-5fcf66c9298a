import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite'

import { Avatar, AvatarFallback, AvatarImage, AvatarRow } from './avatar'

const meta = {
    component: Avatar,
    subcomponents: {
        AvatarRow,
    },
} satisfies Meta<typeof Avatar>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: [
            <AvatarImage
                key="image"
                src="https://github.com/shadcn.png"
                alt="@shadcn"
            />,
            <AvatarFallback key="fallback">CN</AvatarFallback>,
        ],
    },
}

export const Small: Story = {
    args: {
        children: [
            <AvatarImage
                key="image"
                src="https://github.com/shadcn.png"
                alt="@shadcn"
            />,
            <AvatarFallback key="fallback">CN</AvatarFallback>,
        ],
        size: 'sm',
    },
}

export const Row: Story = {
    render: () => (
        <AvatarRow>
            <Avatar>
                <AvatarImage
                    src="https://github.com/shadcn.png"
                    alt="@shadcn"
                />
                <AvatarFallback>CN</AvatarFallback>
            </Avatar>
            <Avatar>
                <AvatarImage
                    src="brokenlink"
                    alt="@shadcn"
                />
                <AvatarFallback>CN</AvatarFallback>
            </Avatar>
            <Avatar>
                <AvatarImage
                    src="https://github.com/shadcn.png"
                    alt="@shadcn"
                />
                <AvatarFallback>CN</AvatarFallback>
            </Avatar>
        </AvatarRow>
    ),
}
