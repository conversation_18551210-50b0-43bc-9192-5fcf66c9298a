import * as React from 'react'
import * as SliderPrimitive from '@radix-ui/react-slider'
import { cn } from '@/shared/lib/css'
import { useCallback, useState } from 'react'

type SliderProps = React.ComponentProps<typeof SliderPrimitive.Root> & {
    marks?: MarkProps[]
}

const MARKS_WIDTH = 140
const MARKS_TOP = 20
const THUMB_SIZE = 28

function Slider({ className, defaultValue, value, marks, min = 0, max, ...props }: SliderProps) {
    const _values = React.useMemo(
        () =>
            Array.isArray(value) ? value : Array.isArray(defaultValue) ? defaultValue : [min, max],
        [
            value,
            defaultValue,
            min,
            max,
        ],
    )
    const [markHeight, setMarkHeight] = useState(0)

    // Вычисление bottom padding для слайдера из высоты marks
    const measureMarkHeight = useCallback(
        (node: HTMLDivElement | null) => {
            if (node && markHeight === 0) {
                const height = node.getBoundingClientRect().height
                setMarkHeight(height)
            }
        },
        [markHeight],
    )

    const sliderPaddingBottom = marks ? MARKS_TOP + markHeight : 0

    return (
        <SliderPrimitive.Root
            className={cn(`relative flex w-full touch-none select-none items-center`, className)}
            value={value}
            max={marks ? marks.length - 1 : max}
            min={marks ? 0 : min}
            style={{
                marginTop: THUMB_SIZE / 2,
                paddingBottom: sliderPaddingBottom,
                marginRight: marks && MARKS_WIDTH / 2 - THUMB_SIZE / 2,
                marginLeft: marks && MARKS_WIDTH / 2 - THUMB_SIZE / 2,
            }}
            {...props}
        >
            <SliderPrimitive.Track className="relative h-1 w-full grow overflow-hidden rounded-full bg-primary/20">
                <SliderPrimitive.Range className="absolute h-full bg-primary" />
            </SliderPrimitive.Track>

            {marks && (
                <div
                    className="absolute flex grow w-full items-center justify-between z-10 px-[12px]"
                    style={{ top: MARKS_TOP }}
                >
                    {marks.map((mark, index) => {
                        return (
                            <div
                                className="relative group"
                                data-active={_values[0] === index}
                                key={mark.title}
                            >
                                <div
                                    className="absolute flex flex-col -translate-x-1/2 text-center text-sm"
                                    style={{ width: MARKS_WIDTH }}
                                >
                                    <Mark
                                        ref={index === 0 ? measureMarkHeight : undefined}
                                        title={mark.title}
                                        details={mark.details}
                                    />
                                </div>
                            </div>
                        )
                    })}
                </div>
            )}

            {Array.from({ length: _values.length }, (_, index) => (
                <SliderPrimitive.Thumb
                    data-slot="slider-thumb"
                    key={index}
                    className="border-primary cursor-pointer bg-background block border-3 shrink-0 rounded-full shadow-sm transition-[color,box-shadow] focus-visible:outline-hidden data-disabled:pointer-events-none data-disabled:border-primary/50"
                    style={{ width: THUMB_SIZE, height: THUMB_SIZE }}
                />
            ))}
        </SliderPrimitive.Root>
    )
}

interface MarkProps {
    title: string
    details: string
    className?: string
    ref?: React.Ref<HTMLDivElement>
}

function Mark({ title, details, className, ref }: MarkProps) {
    return (
        <div
            className={className}
            ref={ref}
        >
            <p className="text-foreground/50 group-data-[active=true]:text-foreground">{title}</p>
            <p className="text-muted-foreground/50 group-data-[active=true]:text-muted-foreground">
                {details}
            </p>
        </div>
    )
}

export { Slider }
