import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { RouterProvider } from '@tanstack/react-router'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import * as Sentry from '@sentry/react'
import { router } from './router'
import './globals.css'
import '@xyflow/react/dist/style.css'

const queryClient = new QueryClient()

Sentry.init({
    dsn: import.meta.env.VITE_SENTRY_DSN,
    enabled: import.meta.env.VITE_SENTRY_ENABLED,

    integrations: [Sentry.tanstackRouterBrowserTracingIntegration(router)],
    tracesSampleRate: import.meta.env.VITE_SENTRY_ENABLED,
})

const rootElement = document.querySelector('#root')!
if (!rootElement.innerHTML) {
    const root = createRoot(rootElement, {
        onUncaughtError: Sentry.reactErrorHandler(),
        onCaughtError: Sentry.reactErrorHandler(),
        onRecoverableError: Sentry.reactErrorHandler(),
    })

    root.render(
        <StrictMode>
            <QueryClientProvider client={queryClient}>
                <RouterProvider router={router} />
            </QueryClientProvider>
        </StrictMode>,
    )
}
