@import 'tailwindcss';
@import 'tw-animate-css';
@plugin 'tailwind-scrollbar';

@custom-variant dark (&:is(.dark *));

:root {
    --header-height: 60px;
    --sidebar-width-expanded: 300px;
    --sidebar-width-collapsed: 75px;
    --graph-sidebar-width-expanded: 300px;
    --graph-sidebar-width-collapsed: 50px;
}

@theme inline {
    --radius-sm: calc(var(--radius) - 4px);
    --radius-md: calc(var(--radius) - 2px);
    --radius-lg: var(--radius);
    --radius-xl: calc(var(--radius) + 4px);
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);
    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);
    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);
    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);
    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);
    --color-destructive: var(--destructive);
    --color-badge-green: var(--badge-green);
    --color-badge-orange: var(--badge-orange);
    --color-badge-red: var(--badge-red);
    --color-raciq-blue: var(--raciq-blue);
    --color-raciq-green: var(--raciq-green);
    --color-raciq-orange: var(--raciq-orange);
    --color-raciq-gray: var(--raciq-gray);
    --color-raciq-red: var(--raciq-red);
    --color-button-accent: var(--button-accent);
    --color-button-neutral: var(--button-neutral);
    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);
    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);
    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-primary-active: var(--sidebar-primary-active);
    --color-sidebar-primary-foreground-active: var(--sidebar-primary-foreground-active);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-toggle-gray: var(--toggle-gray);
    --color-switch-checked: var(--switch-checked);
    --color-switch-unchecked: var(--switch-unchecked);
    --color-switch-disabled: var(--switch-disabled);
    --gray-light: var(--gray-light);
    --shadow-2xl: var(--shadow-2xl);
    --color-elevated-background: var(--elevated-background);
}

:root {
    --radius: 0.625rem;
    --background: oklch(1 0 0);
    --foreground: oklch(0.3714 0.0089 268.42);
    --card: oklch(1 0 0);
    --sidebar: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.7058 0.1251 256.44);
    --primary-foreground: oklch(1 0 0);
    --secondary: oklch(0.754 0 0);
    --secondary-foreground: oklch(1 0 0);
    --button-accent: oklch(0.7486 0.1137 162.46);
    --button-neutral: oklch(1 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.6547 0.2323 15.68);
    --badge-green: oklch(0.7486 0.1137 162.46);
    --badge-orange: oklch(0.8657 0.1551 86.69);
    --badge-red: oklch(0.7399 0.1587 20.75);
    --raciq-blue: oklch(0.7058 0.1251 256.44);
    --raciq-green: oklch(0.7486 0.1137 162.46);
    --raciq-orange: oklch(0.8657 0.1551 86.69);
    --raciq-gray: oklch(0.703 0.0352 268.53);
    --raciq-red: oklch(0.7399 0.1587 20.75);
    --border: oklch(0.9288 0.0126 255.51);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.3714 0.0089 268.42);
    --sidebar-primary-foreground-active: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.7058 0.1251 256.44);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
    --toggle-gray: oklch(0.6006 0.04 268.02);
    --elevated-background: oklch(0.3714 0.0089 268.42 / 0.1);
    --switch-checked: oklch(0.7058 0.1251 256.44);
    --switch-unchecked: oklch(0.9288 0.0126 255.51);
    --switch-disabled: oklch(0.9288 0.0126 255.51);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --button-accent: oklch(0.7486 0.1137 162.46);
    --button-neutral: oklch(1 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: oklch(0.704 0.191 22.216);
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
}
