import type { StorybookConfig } from '@storybook/react-vite'

const config: StorybookConfig = {
    core: {
        disableTelemetry: true,
    },
    stories: [
        '../src/**/*.stories.@(js|jsx|mjs|ts|tsx)',
        '../src/**/*.mdx',
    ],
    addons: [
        '@storybook/addon-docs',
        '@storybook/addon-a11y',
        '@storybook/addon-vitest',
    ],
    framework: {
        name: '@storybook/react-vite',
        options: {},
    },
    typescript: {
        check: false,
        reactDocgen: 'react-docgen-typescript',
        reactDocgenTypescriptOptions: {
            shouldExtractLiteralValuesFromEnum: true,
            propFilter: (prop) => (prop.parent ? !/node_modules/.test(prop.parent.fileName) : true),
        },
    },
}

export default config
